<?php

namespace Config;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();
$routes->setAutoRoute(true);
//$autoRoutesImproved(true);
// The Auto Routing (Legacy) is very dangerous. It is easy to create vulnerable apps
// where controller filters or CSRF protection are bypassed.


// Disable auto-routing to prevent conflicts
$routes->setAutoRoute(false);

// If you don't want to define all routes, please use the Auto Routing (Improved).
// Set `$autoRoutesImproved` to true in `app/Config/Feature.php` and set the following to true.
// $routes->setAutoRoute(false);

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// Home Routes
$routes->group('', function ($routes) {
    $routes->get('/', 'Home::index');
    $routes->get('login', 'Home::login');
    $routes->post('login', 'Home::login');
    $routes->post('dologin', 'Home::dologin');
    $routes->get('logout', 'Home::logout');
    $routes->get('about', 'Home::about');
    $routes->get('findme/(:any)', 'Home::findme/$1');
    $routes->post('gofindme', 'Home::gofindme');
    $routes->post('open_profile', 'Home::open_profile');
});

// Public Letter View Routes (No authentication required)
$routes->get('view_letter/(:any)/(:any)', 'Letters::public_view/$1/$2');
$routes->get('letters/view_pdf/(:any)', 'Letters::view_pdf/$1');
$routes->post('process_letter/(:any)/(:any)', 'Letters::process_letter/$1/$2');

// Admin Dashboard Routes
// $routes->get('dashboard', 'Admindash::index');

// Users Management Routes
$routes->group('users', function ($routes) {
    $routes->get('', 'Users::index');
    $routes->get('view/(:num)', 'Users::view/$1');
    $routes->post('add_user', 'Users::add_user');
    $routes->post('check_fileno', 'Users::check_fileno');
});


// Public Dakoii Routes
$routes->get('dakoii', 'Dakoii::index');
$routes->post('dlogin', 'Dakoii::login');
$routes->get('dlogout', 'Dakoii::logout');

// Protected Dakoii Routes
$routes->group('', ['filter' => 'auth'], function ($routes) {
    // Dashboard
    $routes->get('ddash', 'Dakoii::ddash');

    // Organizations
    $routes->get('organizations', 'Dakoii::organizations');
    $routes->post('addorg', 'Dakoii::addorg');
    $routes->get('dopen_org/(:any)', 'Dakoii::open_org/$1');
    $routes->post('editorg', 'Dakoii::editorg');
    $routes->post('edit-admin', 'Dakoii::editAdmin');
    $routes->post('daddadmin', 'Dakoii::create_admin');
    $routes->post('dakoii_set_license_status', 'Dakoii::dakoii_set_license_status');

    // Province Management
    $routes->group('provinces', function ($routes) {
        $routes->get('', 'Dakoii::provinces');
        $routes->post('add', 'Dakoii::addProvince');
        $routes->post('edit', 'Dakoii::editProvince');
        $routes->get('delete/(:num)', 'Dakoii::deleteProvince/$1');
        $routes->get('get/(:num)', 'Dakoii::getProvince/$1');
    });

    // District Management
    $routes->group('districts', function ($routes) {
        $routes->get('(:num)', 'Dakoii::districts/$1');
        $routes->get('get/(:num)', 'Dakoii::getDistricts/$1');
        $routes->post('add', 'Dakoii::addDistrict');
        $routes->post('edit', 'Dakoii::editDistrict');
        $routes->get('delete/(:num)', 'Dakoii::deleteDistrict/$1');
    });

    // LLG Management
    $routes->group('llgs', function ($routes) {
        $routes->get('(:num)', 'Dakoii::llgs/$1');
        $routes->post('add', 'Dakoii::addLLG');
        $routes->post('edit', 'Dakoii::editLLG');
        $routes->get('delete/(:num)', 'Dakoii::deleteLLG/$1');
    });

    // Ward Management
    $routes->group('wards', function ($routes) {
        $routes->get('(:num)', 'Dakoii::wards/$1');
        $routes->post('add', 'Dakoii::addWard');
        $routes->post('edit', 'Dakoii::editWard');
        $routes->get('delete/(:num)', 'Dakoii::deleteWard/$1');
    });

    // Selection Management
    $routes->group('selections', function ($routes) {
        $routes->post('add', 'Dakoii::addSelection');
        $routes->post('update', 'Dakoii::updateSelection');
        $routes->get('delete/(:num)', 'Dakoii::deleteSelection/$1');
    });

    // System User Management
    $routes->post('edit-system-user', 'Dakoii::editSystemUser');

    // Leave Types Management
    $routes->group('dakoii-leave', function ($routes) {
        $routes->get('', 'DakoiiLeaveController::index');
        $routes->get('create', 'DakoiiLeaveController::create');
        $routes->post('store', 'DakoiiLeaveController::store');
        $routes->get('edit/(:num)', 'DakoiiLeaveController::edit/$1');
        $routes->post('update/(:num)', 'DakoiiLeaveController::update/$1');
        $routes->get('delete/(:num)', 'DakoiiLeaveController::delete/$1');
        $routes->get('restore/(:num)', 'DakoiiLeaveController::restore/$1');
    });
});

// API Routes
$routes->group('api', function ($routes) {
    $routes->post('get_provinces', 'Api::get_provinces');
    $routes->post('get_countries', 'Api::get_countries');
    $routes->post('get_districts', 'Api::get_districts');
    $routes->post('get_llgs', 'Api::get_llgs');
});

// Add these routes after your existing routes
$routes->group('', function ($routes) {
    // Admin routes (protected by auth filter)
    $routes->get('dashboard', 'AdminController::admin_dashboard', ['filter' => 'auth']);
});

// Add these admin routes
$routes->group('', ['filter' => 'auth'], function ($routes) {
    // Admin Dashboard
    $routes->get('dashboard', 'AdminController::admin_dashboard');
    $routes->get('admin/dashboard', 'AdminController::admin_dashboard'); // alternative route
    $routes->get('employees_due_leave', 'AdminController::employees_due_leave');
    $routes->get('employees_due_leave_next_year', 'AdminController::employees_due_leave_next_year');

    // Employee Routes
    $routes->group('employees', function ($routes) {
        $routes->get('', 'Employees::index');
        $routes->get('get_employees', 'Employees::getEmployees');
        $routes->post('save', 'Employees::save');
        $routes->get('get/(:num)', 'Employees::get/$1');
        $routes->post('update/(:num)', 'Employees::update/$1');
        $routes->post('reset-password/(:num)', 'Employees::resetPassword/$1');
        $routes->post('import', 'Employees::import');
    });

    // Leave Management Routes
    $routes->group('leave', function ($routes) {
        $routes->get('', 'Leave::index');
        $routes->get('employee/(:num)', 'Leave::employee/$1');
        $routes->get('create/(:num)', 'Leave::create/$1');
        $routes->post('store', 'Leave::store');
        $routes->get('edit/(:num)', 'Leave::edit/$1');
        $routes->post('update/(:num)', 'Leave::update/$1');
        $routes->get('delete/(:num)', 'Leave::delete/$1');
    });
});

// Groups Management Routes
$routes->group('groups', ['filter' => 'auth'], function ($routes) {
    $routes->get('/', 'Groups::index');
    $routes->post('store', 'Groups::store');
    $routes->post('update/(:num)', 'Groups::update/$1');
    $routes->get('delete/(:num)', 'Groups::delete/$1');
});

// Positions Management Routes
$routes->group('positions', ['filter' => 'auth'], function ($routes) {
    $routes->get('index/(:num)', 'Positions::index/$1');
    $routes->post('store', 'Positions::store');
    $routes->get('edit/(:num)', 'Positions::edit/$1');
    $routes->post('update/(:num)', 'Positions::update/$1');
    $routes->get('delete/(:num)', 'Positions::delete/$1');
    $routes->post('appoint', 'Positions::appoint');
    $routes->get('vacate/(:num)', 'Positions::vacate/$1');
});

// Add direct route for appointments
$routes->get('appointments', 'Positions::appointments', ['filter' => 'auth']);
$routes->get('positions/position_appoint_employee/(:num)', 'Positions::position_appoint_employee/$1', ['filter' => 'auth']);

// Payslips Management Routes
$routes->group('payslips', ['filter' => 'auth'], function ($routes) {
    $routes->get('/', 'Payslips::index');
    $routes->post('store', 'Payslips::store');
    $routes->get('download/(:num)', 'Payslips::download/$1');
    $routes->get('delete/(:num)', 'Payslips::delete/$1');
});

// Letters Management Routes
$routes->group('letters', ['filter' => 'auth'], function ($routes) {
    $routes->get('/', 'Letters::index');
    $routes->get('manage_letters', 'Letters::manage_letters');
});

// Employee Portal Routes
$routes->post('employee_portal/search', 'EmployeePortal::search');
$routes->post('employee_portal/login', 'EmployeePortal::login');

// Protected Employee Portal Routes
$routes->group('employee_portal', ['filter' => 'auth:employee'], function($routes) {
    $routes->get('dashboard', 'EmployeePortal::dashboard');
    $routes->get('logout', 'EmployeePortal::logout');

    // Payslips routes
    $routes->get('my_payslips', 'MyPayslips::view');
    $routes->get('my_payslips/download/(:num)', 'MyPayslips::download/$1');
    $routes->get('my_payslips/getPayslipsByDateRange', 'MyPayslips::getPayslipsByDateRange');
    $routes->get('my_payslips/checkPayslip/(:num)', 'MyPayslips::checkPayslip/$1');

    // Profile routes
    $routes->get('my_profile', 'MyProfile::index');
    $routes->post('my_profile/update', 'MyProfile::update');
    $routes->post('my_profile/update_photo', 'MyProfile::update_photo');

    // Letters routes
    $routes->get('my_letters', 'MyLetters::view');
    $routes->post('my_letters/request_confirmation', 'MyLetters::request_confirmation');
    $routes->post('my_letters/request_other', 'MyLetters::request_other');
    $routes->get('my_letters/download/(:num)', 'MyLetters::download/$1');
});

// Organization Settings Routes
$routes->get('settings/organization', 'OrgSettings::org_settings');
$routes->post('settings/organization/update', 'OrgSettings::update');

// Salaries Routes
$routes->group('salaries', ['filter' => 'auth'], function ($routes) {
    $routes->get('salary', 'SalariesController::salary_analysis');
    $routes->get('salary_analysis', 'SalariesController::salary_analysis');
    $routes->post('salary_analysis', 'SalariesController::salary_analysis');
    $routes->post('getSalaryAnalysisData', 'SalariesController::getSalaryAnalysisData');
});

// Reports Routes
$routes->group('reports', ['filter' => 'auth'], function ($routes) {
    $routes->get('salary', 'SalariesController::salary_analysis');
    $routes->post('salary', 'SalariesController::salary_analysis');
    $routes->post('getSalaryAnalysisData', 'SalariesController::getSalaryAnalysisData');
});

// Add upgrade test routes
$routes->get('upgradetest', 'UpgradeTest::getIndex');
$routes->get('upgradetest/json', 'UpgradeTest::getJson');

/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (is_file(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
    require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}
