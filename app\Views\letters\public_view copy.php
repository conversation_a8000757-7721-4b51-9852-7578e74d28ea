<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' : '' ?>GovPSS</title>
    <link rel="shortcut icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico" type="image/x-icon">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">

    <style>
        :root {
            --png-red: #CE1126;
            --png-black: #000000;
            --png-gold: #FCD116;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }

        .gradient-bg {
            background: linear-gradient(135deg, rgba(206, 17, 38, 0.9), rgba(0, 0, 0, 0.9));
        }

        .btn-primary {
            background-color: var(--png-red);
            border-color: var(--png-red);
        }

        .btn-primary:hover {
            background-color: #a00d1d;
            border-color: #a00d1d;
        }

        .text-primary {
            color: var(--png-red) !important;
        }

        .border-primary {
            border-color: var(--png-red) !important;
        }

        .form-control:focus {
            border-color: var(--png-red);
            box-shadow: 0 0 0 0.25rem rgba(206, 17, 38, 0.25);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url() ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="Logo" height="40" class="me-2">
                <span>GovPSS</span>
            </a>
        </div>
    </nav>

    <!-- Header Banner -->
    <div class="gradient-bg py-3">
        <h4 class="text-center text-white mb-0">LETTER REVIEW AND CONFIRMATION</h4>
    </div>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h4 class="fw-bold">Letter Details</h4>
                            <p class="text-muted">Please review the letter details below</p>
                        </div>

                        <?php if (session()->getFlashdata('error')): ?>
                            <div class="alert alert-danger" role="alert">
                                <?= session()->getFlashdata('error') ?>
                            </div>
                        <?php endif; ?>

                        <!-- Letter Content -->
                        <div class="mb-4 p-4 bg-white rounded border">
                            <div class="mb-4">
                                <h5>Letter Details</h5>
                                <hr>
                                <div class="mb-2">
                                    <strong>Letter Type:</strong> <?= ucfirst(esc($letter['letter_type'])) ?> Letter
                                </div>
                                <div class="mb-2">
                                    <strong>Date Created:</strong> <?= date('F d, Y', strtotime($letter['created_at'])) ?>
                                </div>
                                <div class="mb-2">
                                    <strong>Address To:</strong><br>
                                    <?= nl2br(esc($letter['address_to'])) ?>
                                </div>
                                <div class="mb-2">
                                    <strong>Subject:</strong><br>
                                    <?= esc($letter['subject']) ?>
                                </div>
                                <div class="mb-2">
                                    <strong>Content:</strong><br>
                                    <?= nl2br(esc($letter['content'])) ?>
                                </div>
                            </div>
                        </div>

                        <!-- Process Form -->
                        <form action="<?= base_url('process_letter/' . $letter['unique_code'] . '/' . urlencode($toEmail)) ?>" method="POST" id="letterForm">
                            <?= csrf_field() ?>
                            
                            <div class="mb-4">
                                <label for="remarks" class="form-label">Remarks <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3" required 
                                    placeholder="Enter your remarks/comments about the letter"></textarea>
                                <div class="invalid-feedback">Please provide your remarks.</div>
                            </div>

                            <!-- Hidden status field -->
                            <input type="hidden" name="status" id="letterStatus" value="">

                            <div class="d-flex gap-3">
                                <button type="button" class="btn btn-success flex-grow-1" onclick="handleSubmit('approved')">
                                    <i class="fas fa-check me-2"></i>Approve
                                </button>
                                <button type="button" class="btn btn-danger flex-grow-1" onclick="handleSubmit('rejected')">
                                    <i class="fas fa-times me-2"></i>Reject
                                </button>
                            </div>
                        </form>

                        <!-- Add this JavaScript for form validation -->
                        <script>
                        function handleSubmit(status) {
                            const remarks = document.getElementById('remarks').value.trim();
                            if (!remarks) {
                                Swal.fire({
                                    title: 'Error!',
                                    text: 'Please provide remarks before ' + (status === 'approved' ? 'approving' : 'rejecting') + ' the letter.',
                                    icon: 'error',
                                    confirmButtonColor: '#CE1126'
                                });
                                return;
                            }
                            
                            // Set the status value
                            document.getElementById('letterStatus').value = status;
                            
                            // Show confirmation dialog
                            Swal.fire({
                                title: 'Confirm ' + (status === 'approved' ? 'Approval' : 'Rejection'),
                                text: 'Are you sure you want to ' + (status === 'approved' ? 'approve' : 'reject') + ' this letter?',
                                icon: 'question',
                                showCancelButton: true,
                                confirmButtonColor: status === 'approved' ? '#28a745' : '#dc3545',
                                cancelButtonColor: '#6c757d',
                                confirmButtonText: 'Yes, ' + (status === 'approved' ? 'approve' : 'reject') + ' it!',
                                cancelButtonText: 'Cancel'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    // Debug logging
                                    console.log('Form submission - Status:', status);
                                    console.log('Form submission - Remarks:', remarks);
                                    console.log('Form submission - Action:', document.getElementById('letterForm').action);
                                    
                                    // Submit the form
                                    document.getElementById('letterForm').submit();
                                }
                            });
                        }
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <div class="mb-3">
                <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Dakoii" height="32">
            </div>
            <p class="small mb-1">&copy; 2024 <a href="https://www.dakoiims.com" class="text-warning text-decoration-none">Dakoii Systems</a></p>
            <p class="small mb-0"><?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?></p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

    <!-- SweetAlert Messages -->
    <script>
        <?php if (session()->getFlashdata('success')): ?>
            Swal.fire({
                title: 'Success!',
                text: '<?= session()->getFlashdata('success') ?>',
                icon: 'success',
                confirmButtonColor: '#CE1126'
            });
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            Swal.fire({
                title: 'Error!',
                text: '<?= session()->getFlashdata('error') ?>',
                icon: 'error',
                confirmButtonColor: '#CE1126'
            });
        <?php endif; ?>
    </script>
</body>
</html> 